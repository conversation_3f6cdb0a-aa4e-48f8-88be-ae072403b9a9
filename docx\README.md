# 浓烟环境人体目标判别系统

## 🎯 项目概述

本项目成功开发了一个基于YOLOv8n的浓烟环境人体检测模型，专门用于在烟雾环境中准确识别和定位人体目标。该系统在去雾后的数据集上训练，实现了优异的检测性能。

## 🏆 主要成果

### 模型性能
- **mAP@0.5**: 99.50% 
- **mAP@0.5:0.95**: 95.25%
- **精度**: 99.96%
- **召回率**: 100.00%
- **推理速度**: 79.55 FPS
- **模型大小**: 5.94 MB

### 训练成果
- ✅ 100轮训练成功完成
- ✅ 无过拟合现象
- ✅ 收敛稳定
- ✅ 性能优异

## 📁 项目结构

```
训练去烟后数据集的人体识别模型/
├── 📄 train_model.py              # 主训练脚本
├── 📄 evaluate_model.py           # 模型评估脚本
├── 📄 generate_analysis_plots.py  # 可视化分析脚本
├── 📄 dataset.yaml               # 数据集配置
├── 📄 dehazing.py               # 去雾处理脚本
├── 📂 wuxi_video_2_dehazed/     # 数据集 (3,187张图像)
├── 📂 model/                    # 训练输出
│   └── human_detection_model/   # 模型文件和结果
├── 📂 evaluation_results/       # 评估结果
├── 📂 docx/                    # 📚 项目文档
│   ├── README.md               # 本文档
│   ├── 项目文档.md              # 项目总览
│   ├── 训练结果完整报告.md       # 详细训练报告
│   ├── 技术规格与部署指南.md     # 部署指南
│   ├── 训练损失曲线.png         # 训练可视化
│   └── 性能指标曲线.png         # 性能可视化
└── 📂 runs/                    # YOLO运行结果
```

## 🚀 快速开始

### 1. 环境准备
```bash
pip install ultralytics torch torchvision opencv-python
```

### 2. 模型推理
```python
from ultralytics import YOLO

# 加载训练好的模型
model = YOLO('model/human_detection_model/weights/best.pt')

# 单张图像检测
results = model('your_image.jpg')
results[0].show()

# 批量检测
results = model(['img1.jpg', 'img2.jpg'])

# 实时检测
results = model(source=0, show=True)  # 摄像头
```

### 3. 性能评估
```bash
python evaluate_model.py
```

## 📊 训练结果分析

### 损失函数变化
- **Box Loss**: 0.842 → 0.299 (下降64.5%)
- **Class Loss**: 2.553 → 0.203 (下降92.1%)
- **DFL Loss**: 1.008 → 0.783 (下降22.3%)

### 关键里程碑
- **第1轮**: 初始性能建立
- **第30轮**: 快速收敛阶段完成
- **第87轮**: 达到最佳性能 (mAP@0.5:0.95 = 94.64%)
- **第100轮**: 训练完成，性能稳定

### 数据集信息
- **训练集**: 2,245张图像 (70.4%)
- **验证集**: 801张图像 (25.1%)
- **测试集**: 141张图像 (4.4%)
- **类别**: 1类 (人体检测)

## 🔧 技术规格

### 模型架构
- **基础模型**: YOLOv8n (Nano版本)
- **参数量**: 3,005,843个
- **计算复杂度**: 8.1 GFLOPs
- **输入尺寸**: 640×640×3

### 硬件要求
- **最低配置**: CPU推理，8GB RAM
- **推荐配置**: NVIDIA GTX 1660+，16GB RAM
- **测试环境**: RTX 4060 Laptop GPU

## 📈 性能对比

| 指标 | 验证集 | 测试集 |
|------|--------|--------|
| mAP@0.5 | 99.50% | 99.50% |
| mAP@0.5:0.95 | 94.60% | 95.25% |
| 精度 | 99.99% | 99.96% |
| 召回率 | 99.88% | 100.00% |
| F1分数 | 99.93% | 99.98% |

## 🎨 可视化结果

项目包含以下可视化内容：
- 📈 训练损失曲线图
- 📊 性能指标变化图
- 🎯 混淆矩阵
- 📋 精度-召回率曲线
- 🖼️ 检测结果示例

## 🚀 部署选项

### 1. 本地部署
```python
# 直接使用PyTorch模型
model = YOLO('best.pt')
```

### 2. API服务
```python
# Flask/FastAPI服务
from flask import Flask
app = Flask(__name__)
model = YOLO('best.pt')
```

### 3. 优化部署
```python
# TensorRT优化 (NVIDIA GPU)
model.export(format='engine')

# ONNX格式 (跨平台)
model.export(format='onnx')
```

## 📚 文档导航

| 文档 | 描述 |
|------|------|
| [项目文档.md](./项目文档.md) | 项目总览和基础信息 |
| [训练结果完整报告.md](./训练结果完整报告.md) | 详细的训练过程分析 |
| [技术规格与部署指南.md](./技术规格与部署指南.md) | 技术规格和部署说明 |

## 🔍 应用场景

- 🚨 **应急救援**: 烟雾环境中的人员搜救
- 🏭 **工业安全**: 工厂烟雾环境监控
- 🔥 **消防安全**: 火灾现场人员检测
- 🏢 **建筑安全**: 烟雾报警系统集成
- 📹 **智能监控**: 恶劣环境下的安防系统

## ⚡ 性能优势

1. **高精度**: mAP@0.5:0.95达到95.25%
2. **高速度**: 79.55 FPS实时检测
3. **轻量化**: 5.94MB模型大小
4. **稳定性**: 无过拟合，泛化能力强
5. **易部署**: 支持多种部署方式

## 🛠️ 故障排除

### 常见问题
1. **CUDA内存不足**: 使用CPU推理或减少批次大小
2. **推理速度慢**: 启用半精度推理或使用TensorRT
3. **检测精度低**: 调整置信度阈值

### 性能监控
```python
# 监控推理性能
import time
start = time.time()
results = model('image.jpg')
print(f"推理时间: {time.time() - start:.3f}s")
```

## 📞 技术支持

- **项目维护**: 浓烟环境人体目标判别项目组
- **技术咨询**: 详见技术规格与部署指南
- **问题反馈**: 通过GitHub Issues提交

## 📄 许可证

本项目遵循相关开源许可证，具体请查看LICENSE文件。

---

**🎉 项目状态**: ✅ 训练完成，性能优异，可投入使用  
**📅 最后更新**: 2025年6月14日  
**🏷️ 版本**: v1.0  
**👥 开发团队**: 浓烟环境人体目标判别项目组
