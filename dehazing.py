#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
去烟算法模块 - 浓烟环境人体目标判别系统

文件描述：
    实现多种去烟/去雾算法，专门针对浓烟环境下的图像处理进行优化。
    包含暗通道先验(DCP)算法和AOD-Net深度学习网络两种主要方法。

主要功能：
    1. 暗通道先验去雾算法 - 基于物理模型的传统方法
    2. AOD-Net深度学习网络 - 端到端的神经网络方法
    3. 图像质量评估 - 提供多种图像质量指标计算
    4. 统一处理接口 - 封装不同算法的调用方式

作者：浓烟环境人体目标判别项目组
创建日期：2025年6月13日
最后修改：2025年6月14日
版本：v1.0

依赖库：
    - OpenCV: 图像处理和计算机视觉
    - NumPy: 数值计算
    - PyTorch: 深度学习框架
    - typing: 类型注解支持
"""

import cv2
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional
import time

class DarkChannelPrior:
    """
    暗通道先验去雾算法 (Dark Channel Prior)

    基于He等人提出的暗通道先验理论，专门针对浓烟环境进行了参数优化。
    该算法基于一个重要观察：在大多数非天空的局部区域里，某些像素总会有至少
    一个颜色通道具有很低的值。

    算法原理：
        1. 计算输入图像的暗通道
        2. 估计全局大气光值
        3. 估计透射率图
        4. 使用导向滤波细化透射率
        5. 根据大气散射模型恢复场景辐射

    适用场景：
        - 浓烟环境下的图像去雾
        - 雾霾天气的图像增强
        - 水下图像的清晰化处理

    性能特点：
        - 处理速度快，适合实时应用
        - 对浓烟环境效果显著
        - 无需训练数据，即插即用

    属性：
        patch_size (int): 暗通道计算时的邻域窗口大小，影响去雾效果的细腻程度
        omega (float): 去雾强度参数，控制去雾的程度，范围[0,1]
        t0 (float): 透射率下限，防止过度去雾导致的失真
    """

    def __init__(self, patch_size: int = 15, omega: float = 0.95, t0: float = 0.1):
        """
        初始化暗通道先验算法参数

        Args:
            patch_size (int): 暗通道计算的邻域大小，通常取15。
                            较大的值会产生更平滑的结果，但可能丢失细节；
                            较小的值保留更多细节，但可能产生噪声。
            omega (float): 去雾强度参数，范围[0,1]，默认0.95。
                          值越大去雾效果越强，但可能导致过度处理；
                          值越小保留更多原始信息，但去雾效果较弱。
            t0 (float): 透射率下限，范围[0,1]，默认0.1。
                       防止透射率过小导致的噪声放大和颜色失真。

        注意：
            参数设置需要根据具体的烟雾浓度和场景特点进行调整。
            对于浓烟环境，建议适当降低omega值以避免过度处理。
        """
        self.patch_size = patch_size  # 邻域窗口大小
        self.omega = omega           # 去雾强度系数
        self.t0 = t0                # 透射率下限阈值
        
    def get_dark_channel(self, image: np.ndarray) -> np.ndarray:
        """
        计算输入图像的暗通道

        暗通道定义：对于任意的非天空区域，在每个像素的RGB三个通道中，
        至少有一个通道的强度值很小。暗通道就是每个像素RGB三通道的最小值
        在局部邻域内的最小值。

        Args:
            image (np.ndarray): 输入图像，BGR格式，shape为(H, W, 3)

        Returns:
            np.ndarray: 暗通道图像，灰度图，shape为(H, W)

        算法步骤：
            1. 对每个像素取RGB三通道的最小值
            2. 在指定邻域内取最小值（使用形态学腐蚀操作）
        """
        # 对每个像素取RGB三通道的最小值，得到最小值通道图
        min_channel = np.min(image, axis=2)

        # 创建矩形结构元素，用于形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (self.patch_size, self.patch_size))

        # 使用腐蚀操作在邻域内取最小值，得到暗通道
        dark_channel = cv2.erode(min_channel, kernel)

        return dark_channel

    def estimate_atmospheric_light(self, image: np.ndarray, dark_channel: np.ndarray) -> np.ndarray:
        """
        估计全局大气光值

        大气光是指无穷远处的天空亮度，在图像去雾中是一个重要参数。
        通过在暗通道中选择最亮的像素点，然后在原图中找到对应位置的像素值。

        Args:
            image (np.ndarray): 原始输入图像，BGR格式，shape为(H, W, 3)
            dark_channel (np.ndarray): 暗通道图像，shape为(H, W)

        Returns:
            np.ndarray: 大气光值，shape为(3,)，对应BGR三个通道

        算法步骤：
            1. 在暗通道中选择最亮的0.1%像素
            2. 在原图中找到这些像素对应的位置
            3. 计算这些像素的平均值作为大气光
        """
        h, w = dark_channel.shape
        num_pixels = h * w

        # 选择暗通道中最亮的0.1%像素，至少选择1个像素
        num_brightest = max(1, num_pixels // 1000)

        # 将暗通道展平并找到最亮像素的索引
        flat_dark = dark_channel.flatten()
        indices = np.argpartition(flat_dark, -num_brightest)[-num_brightest:]

        # 在原图中找到对应位置的像素值
        brightest_pixels = []
        for idx in indices:
            y, x = divmod(idx, w)  # 将一维索引转换为二维坐标
            brightest_pixels.append(image[y, x])

        brightest_pixels = np.array(brightest_pixels)

        # 计算最亮像素的均值作为大气光估计值
        atmospheric_light = np.mean(brightest_pixels, axis=0)

        return atmospheric_light
        
    def estimate_transmission(self, image: np.ndarray, atmospheric_light: np.ndarray) -> np.ndarray:
        """
        估计场景透射率

        透射率表示光线在传播过程中没有被散射的部分比例。
        根据暗通道先验理论，可以通过归一化图像的暗通道来估计透射率。

        Args:
            image (np.ndarray): 原始输入图像，BGR格式，shape为(H, W, 3)
            atmospheric_light (np.ndarray): 大气光值，shape为(3,)

        Returns:
            np.ndarray: 透射率图，shape为(H, W)，值域[0, 1]

        算法原理：
            根据大气散射模型：I(x) = J(x)t(x) + A(1-t(x))
            其中t(x)为透射率，通过暗通道先验可估计为：
            t(x) = 1 - ω * dark_channel(I(x)/A)
        """
        # 将图像归一化到大气光，避免除零错误
        normalized_image = image.astype(np.float64) / (atmospheric_light + 1e-8)

        # 计算归一化图像的暗通道
        dark_channel = self.get_dark_channel(normalized_image)

        # 根据暗通道先验公式估计透射率
        # omega参数控制去雾强度，保留少量雾气以保持自然效果
        transmission = 1 - self.omega * dark_channel

        return transmission

    def refine_transmission(self, image: np.ndarray, transmission: np.ndarray) -> np.ndarray:
        """
        使用导向滤波细化透射率图

        原始透射率估计可能存在块状效应和边缘不平滑的问题。
        导向滤波可以在保持边缘的同时平滑透射率图。

        Args:
            image (np.ndarray): 原始图像，用作导向图，shape为(H, W, 3)
            transmission (np.ndarray): 原始透射率图，shape为(H, W)

        Returns:
            np.ndarray: 细化后的透射率图，shape为(H, W)

        注意：
            需要安装opencv-contrib-python才能使用导向滤波功能
        """
        # 将原始图像转换为灰度图作为导向图
        guide = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY).astype(np.float32) / 255.0
        transmission = transmission.astype(np.float32)

        # 使用导向滤波进行边缘保持的平滑
        # radius: 滤波半径，控制平滑程度
        # eps: 正则化参数，防止除零并控制平滑强度
        refined = cv2.ximgproc.guidedFilter(guide, transmission, radius=60, eps=0.0001)

        return refined
        
    def recover_scene(self, image: np.ndarray, transmission: np.ndarray, 
                     atmospheric_light: np.ndarray) -> np.ndarray:
        """恢复场景辐射"""
        # 确保透射率不小于t0
        transmission = np.maximum(transmission, self.t0)
        
        # 扩展透射率到三通道
        if len(transmission.shape) == 2:
            transmission = np.stack([transmission] * 3, axis=2)
            
        # 场景辐射恢复
        recovered = np.zeros_like(image, dtype=np.float64)
        
        for c in range(3):
            recovered[:, :, c] = (image[:, :, c].astype(np.float64) - atmospheric_light[c]) / transmission[:, :, c] + atmospheric_light[c]
            
        # 限制到有效范围
        recovered = np.clip(recovered, 0, 255)
        
        return recovered.astype(np.uint8)
        
    def dehaze(self, image: np.ndarray) -> Tuple[np.ndarray, dict]:
        """
        执行去雾处理
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            去雾后的图像和处理信息
        """
        start_time = time.time()
        
        # 计算暗通道
        dark_channel = self.get_dark_channel(image)
        
        # 估计大气光
        atmospheric_light = self.estimate_atmospheric_light(image, dark_channel)
        
        # 估计透射率
        transmission = self.estimate_transmission(image, atmospheric_light)
        
        # 细化透射率（如果opencv-contrib可用）
        try:
            transmission = self.refine_transmission(image, transmission)
        except:
            pass  # 如果没有导向滤波，使用原始透射率
            
        # 恢复场景
        dehazed = self.recover_scene(image, transmission, atmospheric_light)
        
        processing_time = time.time() - start_time
        
        info = {
            "algorithm": "Dark Channel Prior",
            "processing_time_ms": processing_time * 1000,
            "atmospheric_light": atmospheric_light.tolist(),
            "transmission_mean": float(np.mean(transmission)),
            "transmission_std": float(np.std(transmission))
        }
        
        return dehazed, info

class AODNet(nn.Module):
    """
    AOD-Net: All-in-One Dehazing Network
    基于深度学习的端到端去雾网络
    """
    
    def __init__(self):
        super(AODNet, self).__init__()
        
        # 编码器
        self.conv1 = nn.Conv2d(3, 3, 1)
        self.conv2 = nn.Conv2d(3, 3, 3, padding=1)
        self.conv3 = nn.Conv2d(6, 3, 5, padding=2)
        self.conv4 = nn.Conv2d(6, 3, 7, padding=3)
        self.conv5 = nn.Conv2d(12, 3, 3, padding=1)
        
        # 激活函数
        self.relu = nn.ReLU(inplace=True)
        
    def forward(self, x):
        """前向传播"""
        x1 = self.relu(self.conv1(x))
        x2 = self.relu(self.conv2(x))
        
        cat1 = torch.cat((x1, x2), 1)
        x3 = self.relu(self.conv3(cat1))
        
        cat2 = torch.cat((x2, x3), 1)
        x4 = self.relu(self.conv4(cat2))
        
        cat3 = torch.cat((x1, x2, x3, x4), 1)
        k = self.relu(self.conv5(cat3))
        
        # AOD公式: J = K(I-A) + A, 这里简化为 J = K*I + (1-K)*A
        # 假设A=1（白色大气光）
        output = k * x + (1 - k)
        
        return output

class DehazeProcessor:
    """
    去烟处理器
    集成多种去烟算法，提供统一接口
    """
    
    def __init__(self, method: str = "dcp", device: str = "cpu"):
        """
        初始化去烟处理器
        
        Args:
            method: 去烟方法 ("dcp", "aod")
            device: 计算设备
        """
        self.method = method
        self.device = device
        
        if method == "dcp":
            self.processor = DarkChannelPrior()
        elif method == "aod":
            self.processor = AODNet()
            self.processor.to(device)
            self.processor.eval()
        else:
            raise ValueError(f"不支持的去烟方法: {method}")
            
    def preprocess_image(self, image: np.ndarray) -> torch.Tensor:
        """预处理图像用于深度学习模型"""
        # 归一化到[0,1]
        image_norm = image.astype(np.float32) / 255.0
        
        # 转换为tensor并调整维度 (H,W,C) -> (1,C,H,W)
        image_tensor = torch.from_numpy(image_norm).permute(2, 0, 1).unsqueeze(0)
        
        return image_tensor.to(self.device)
        
    def postprocess_image(self, tensor: torch.Tensor) -> np.ndarray:
        """后处理tensor为图像"""
        # 转换回numpy (1,C,H,W) -> (H,W,C)
        image = tensor.squeeze(0).permute(1, 2, 0).cpu().numpy()
        
        # 反归一化到[0,255]
        image = np.clip(image * 255.0, 0, 255).astype(np.uint8)
        
        return image
        
    def process(self, image: np.ndarray) -> Tuple[np.ndarray, dict]:
        """
        处理图像
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            处理后的图像和信息
        """
        start_time = time.time()
        
        if self.method == "dcp":
            result, info = self.processor.dehaze(image)
        elif self.method == "aod":
            with torch.no_grad():
                # 预处理
                input_tensor = self.preprocess_image(image)
                
                # 推理
                output_tensor = self.processor(input_tensor)
                
                # 后处理
                result = self.postprocess_image(output_tensor)
                
                processing_time = time.time() - start_time
                info = {
                    "algorithm": "AOD-Net",
                    "processing_time_ms": processing_time * 1000,
                    "device": self.device
                }
        
        return result, info
        
    def load_pretrained_weights(self, weights_path: str):
        """加载预训练权重"""
        if self.method == "aod":
            checkpoint = torch.load(weights_path, map_location=self.device)
            self.processor.load_state_dict(checkpoint)
            print(f"已加载预训练权重: {weights_path}")
        else:
            print("当前方法不支持预训练权重")

def calculate_image_quality_metrics(original: np.ndarray, processed: np.ndarray) -> dict:
    """
    计算图像质量评估指标
    
    Args:
        original: 原始图像
        processed: 处理后图像
        
    Returns:
        质量指标字典
    """
    # 转换为灰度图
    if len(original.shape) == 3:
        orig_gray = cv2.cvtColor(original, cv2.COLOR_BGR2GRAY)
        proc_gray = cv2.cvtColor(processed, cv2.COLOR_BGR2GRAY)
    else:
        orig_gray = original
        proc_gray = processed
        
    # 计算PSNR
    mse = np.mean((orig_gray.astype(np.float64) - proc_gray.astype(np.float64)) ** 2)
    if mse == 0:
        psnr = float('inf')
    else:
        psnr = 20 * np.log10(255.0 / np.sqrt(mse))
        
    # 计算SSIM
    ssim = cv2.matchTemplate(orig_gray, proc_gray, cv2.TM_CCOEFF_NORMED)[0, 0]
    
    # 计算信息熵
    def calculate_entropy(img):
        hist = cv2.calcHist([img], [0], None, [256], [0, 256])
        hist = hist.flatten()
        hist = hist[hist > 0]
        prob = hist / hist.sum()
        entropy = -np.sum(prob * np.log2(prob))
        return entropy
        
    orig_entropy = calculate_entropy(orig_gray)
    proc_entropy = calculate_entropy(proc_gray)
    
    # 计算平均梯度
    def calculate_avg_gradient(img):
        grad_x = cv2.Sobel(img, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(img, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        return np.mean(gradient_magnitude)
        
    orig_gradient = calculate_avg_gradient(orig_gray)
    proc_gradient = calculate_avg_gradient(proc_gray)
    
    metrics = {
        "psnr": float(psnr),
        "ssim": float(ssim),
        "entropy_improvement": float(proc_entropy - orig_entropy),
        "gradient_improvement": float(proc_gradient - orig_gradient),
        "original_entropy": float(orig_entropy),
        "processed_entropy": float(proc_entropy),
        "original_avg_gradient": float(orig_gradient),
        "processed_avg_gradient": float(proc_gradient)
    }
    
    return metrics

if __name__ == "__main__":
    # 测试去烟算法
    print("测试去烟算法...")
    
    # 创建测试图像（模拟有雾图像）
    test_image = np.random.randint(50, 200, (480, 640, 3), dtype=np.uint8)
    
    # 测试暗通道先验
    print("\n测试暗通道先验算法...")
    dcp_processor = DehazeProcessor(method="dcp")
    dehazed_dcp, info_dcp = dcp_processor.process(test_image)
    print(f"DCP处理时间: {info_dcp['processing_time_ms']:.2f}ms")
    
    # 计算质量指标
    metrics = calculate_image_quality_metrics(test_image, dehazed_dcp)
    print(f"图像质量改善:")
    print(f"  信息熵提升: {metrics['entropy_improvement']:.3f}")
    print(f"  平均梯度提升: {metrics['gradient_improvement']:.3f}")
    
    print("去烟算法测试完成！")
