#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练结果可视化分析脚本

文件描述：
    生成训练过程的详细可视化图表，包括损失曲线、性能指标变化等。

作者：浓烟环境人体目标判别项目组
创建日期：2025年6月15日
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import os

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def load_training_results():
    """加载训练结果数据"""
    results_path = 'model/human_detection_model/results.csv'
    if not os.path.exists(results_path):
        print(f"错误：结果文件不存在 {results_path}")
        return None

    df = pd.read_csv(results_path)
    return df

def create_loss_curves(df):
    """创建损失函数曲线图"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('训练损失函数变化曲线', fontsize=16, fontweight='bold')

    # 训练损失
    axes[0, 0].plot(df['epoch'], df['train/box_loss'], 'b-', linewidth=2, label='Box Loss')
    axes[0, 0].plot(df['epoch'], df['train/cls_loss'], 'r-', linewidth=2, label='Class Loss')
    axes[0, 0].plot(df['epoch'], df['train/dfl_loss'], 'g-', linewidth=2, label='DFL Loss')
    axes[0, 0].set_title('训练损失', fontweight='bold')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 验证损失
    axes[0, 1].plot(df['epoch'], df['val/box_loss'], 'b-', linewidth=2, label='Val Box Loss')
    axes[0, 1].plot(df['epoch'], df['val/cls_loss'], 'r-', linewidth=2, label='Val Class Loss')
    axes[0, 1].plot(df['epoch'], df['val/dfl_loss'], 'g-', linewidth=2, label='Val DFL Loss')
    axes[0, 1].set_title('验证损失', fontweight='bold')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('Loss')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # Box损失对比
    axes[1, 0].plot(df['epoch'], df['train/box_loss'], 'b-', linewidth=2, label='Train Box Loss')
    axes[1, 0].plot(df['epoch'], df['val/box_loss'], 'r-', linewidth=2, label='Val Box Loss')
    axes[1, 0].set_title('Box损失对比', fontweight='bold')
    axes[1, 0].set_xlabel('Epoch')
    axes[1, 0].set_ylabel('Box Loss')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # Class损失对比
    axes[1, 1].plot(df['epoch'], df['train/cls_loss'], 'b-', linewidth=2, label='Train Class Loss')
    axes[1, 1].plot(df['epoch'], df['val/cls_loss'], 'r-', linewidth=2, label='Val Class Loss')
    axes[1, 1].set_title('Class损失对比', fontweight='bold')
    axes[1, 1].set_xlabel('Epoch')
    axes[1, 1].set_ylabel('Class Loss')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('docx/训练损失曲线.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_metrics_curves(df):
    """创建性能指标曲线图"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('模型性能指标变化曲线', fontsize=16, fontweight='bold')

    # mAP指标
    axes[0, 0].plot(df['epoch'], df['metrics/mAP50(B)'], 'b-', linewidth=2, label='mAP@0.5')
    axes[0, 0].plot(df['epoch'], df['metrics/mAP50-95(B)'], 'r-', linewidth=2, label='mAP@0.5:0.95')
    axes[0, 0].set_title('mAP指标变化', fontweight='bold')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('mAP')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].set_ylim([0.8, 1.0])

    # 精度和召回率
    axes[0, 1].plot(df['epoch'], df['metrics/precision(B)'], 'g-', linewidth=2, label='Precision')
    axes[0, 1].plot(df['epoch'], df['metrics/recall(B)'], 'orange', linewidth=2, label='Recall')
    axes[0, 1].set_title('精度和召回率', fontweight='bold')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('Score')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].set_ylim([0.99, 1.001])

    # 学习率变化
    axes[1, 0].plot(df['epoch'], df['lr/pg0'], 'purple', linewidth=2, label='Learning Rate')
    axes[1, 0].set_title('学习率变化', fontweight='bold')
    axes[1, 0].set_xlabel('Epoch')
    axes[1, 0].set_ylabel('Learning Rate')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # mAP@0.5:0.95详细变化
    axes[1, 1].plot(df['epoch'], df['metrics/mAP50-95(B)'], 'r-', linewidth=2)
    axes[1, 1].set_title('mAP@0.5:0.95详细变化', fontweight='bold')
    axes[1, 1].set_xlabel('Epoch')
    axes[1, 1].set_ylabel('mAP@0.5:0.95')
    axes[1, 1].grid(True, alpha=0.3)

    # 标注最佳点
    best_idx = df['metrics/mAP50-95(B)'].idxmax()
    best_epoch = df.loc[best_idx, 'epoch']
    best_map = df.loc[best_idx, 'metrics/mAP50-95(B)']
    axes[1, 1].plot(best_epoch, best_map, 'ro', markersize=8)
    axes[1, 1].annotate(f'Best: Epoch {best_epoch}\nmAP: {best_map:.4f}',
                       xy=(best_epoch, best_map), xytext=(best_epoch+10, best_map-0.01),
                       arrowprops=dict(arrowstyle='->', color='red'))

    plt.tight_layout()
    plt.savefig('docx/性能指标曲线.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_summary_statistics(df):
    """创建训练统计摘要"""
    # 计算关键统计信息
    final_epoch = df['epoch'].max()
    best_map_idx = df['metrics/mAP50-95(B)'].idxmax()
    best_epoch = df.loc[best_map_idx, 'epoch']
    best_map = df.loc[best_map_idx, 'metrics/mAP50-95(B)']

    final_metrics = df.iloc[-1]

    stats = {
        '训练概况': {
            '总训练轮次': final_epoch,
            '最佳轮次': best_epoch,
            '总训练时间(秒)': final_metrics['time'],
            '总训练时间(分钟)': round(final_metrics['time'] / 60, 2)
        },
        '最终性能': {
            'mAP@0.5': final_metrics['metrics/mAP50(B)'],
            'mAP@0.5:0.95': final_metrics['metrics/mAP50-95(B)'],
            '精度': final_metrics['metrics/precision(B)'],
            '召回率': final_metrics['metrics/recall(B)']
        },
        '最佳性能': {
            '最佳mAP@0.5:0.95': best_map,
            '最佳轮次': best_epoch
        },
        '损失变化': {
            '初始Box损失': df.iloc[0]['train/box_loss'],
            '最终Box损失': final_metrics['train/box_loss'],
            'Box损失下降率': f"{(1 - final_metrics['train/box_loss'] / df.iloc[0]['train/box_loss']) * 100:.1f}%",
            '初始Class损失': df.iloc[0]['train/cls_loss'],
            '最终Class损失': final_metrics['train/cls_loss'],
            'Class损失下降率': f"{(1 - final_metrics['train/cls_loss'] / df.iloc[0]['train/cls_loss']) * 100:.1f}%"
        }
    }

    return stats

def main():
    """主函数"""
    print("开始生成训练结果可视化分析...")

    # 创建输出目录
    os.makedirs('docx', exist_ok=True)

    # 加载数据
    df = load_training_results()
    if df is None:
        return

    print("生成损失曲线图...")
    create_loss_curves(df)

    print("生成性能指标曲线图...")
    create_metrics_curves(df)

    print("生成统计摘要...")
    stats = create_summary_statistics(df)

    # 打印统计信息
    print("\n=== 训练统计摘要 ===")
    for category, metrics in stats.items():
        print(f"\n{category}:")
        for key, value in metrics.items():
            print(f"  {key}: {value}")

    print(f"\n可视化图表已保存到 docx/ 目录")
    print("- 训练损失曲线.png")
    print("- 性能指标曲线.png")

if __name__ == "__main__":
    main()