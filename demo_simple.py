#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化演示脚本 - 浓烟环境人体目标判别系统

文件描述：
    提供简单易用的接口，用于演示去烟和人体检测功能。
    用户只需提供输入视频路径，即可获得处理后的输出视频。

使用方法：
    python demo_simple.py input_video.mp4

作者：浓烟环境人体目标判别项目组
创建日期：2025年6月17日
版本：v1.0
"""

import os
import sys
import time
from datetime import datetime
from video_demo import VideoProcessor

def simple_demo(input_video_path: str, output_dir: str = "demo_output"):
    """
    简化的演示函数

    Args:
        input_video_path: 输入视频路径
        output_dir: 输出目录
    """
    print("=" * 60)
    print("🔥 浓烟环境人体目标判别系统 - 视频演示")
    print("=" * 60)

    # 检查输入文件
    if not os.path.exists(input_video_path):
        print(f"❌ 错误：输入视频文件不存在 {input_video_path}")
        return False

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 生成输出文件名
    input_filename = os.path.basename(input_video_path)
    name_without_ext = os.path.splitext(input_filename)[0]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"{name_without_ext}_processed_{timestamp}.mp4"
    output_path = os.path.join(output_dir, output_filename)

    print(f"📹 输入视频: {input_video_path}")
    print(f"📁 输出目录: {output_dir}")
    print(f"🎬 输出文件: {output_filename}")
    print()

    # 检查模型文件
    model_path = "model/human_detection_model/weights/best.pt"
    if not os.path.exists(model_path):
        print(f"❌ 错误：模型文件不存在 {model_path}")
        print("请确保已完成模型训练，或将模型文件放置在正确位置。")
        return False

    print("🚀 开始处理...")
    print("⚙️  使用配置:")
    print(f"   - 去烟算法: 暗通道先验 (DCP)")
    print(f"   - 检测模型: {model_path}")
    print(f"   - 计算设备: CPU")
    print()

    try:
        # 创建视频处理器
        processor = VideoProcessor(
            model_path=model_path,
            dehaze_method="dcp",
            device="cpu",
            queue_size=30
        )

        # 处理视频
        start_time = time.time()
        stats = processor.process_video(input_video_path, output_path)
        total_time = time.time() - start_time

        # 显示结果
        print()
        print("✅ 处理完成！")
        print("=" * 60)
        print("📊 处理统计:")
        print(f"   ⏱️  总处理时间: {total_time:.2f} 秒")
        print(f"   🎞️  处理帧数: {stats['processed_frames']}/{stats['total_frames']}")
        print(f"   🚀 平均处理速度: {stats['fps']:.2f} FPS")
        print(f"   🌫️  平均去烟时间: {stats['avg_dehaze_time']*1000:.2f} ms/帧")
        print(f"   👤 平均检测时间: {stats['avg_detection_time']*1000:.2f} ms/帧")
        print()
        print(f"🎬 输出视频已保存到: {output_path}")

        # 保存统计信息
        stats_path = output_path.replace('.mp4', '_stats.json')
        import json
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        print(f"📈 统计信息已保存到: {stats_path}")

        return True

    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print()

    # 检查命令行参数
    if len(sys.argv) != 2:
        print("使用方法:")
        print(f"  python {sys.argv[0]} <输入视频路径>")
        print()
        print("示例:")
        print(f"  python {sys.argv[0]} test_video.mp4")
        print(f"  python {sys.argv[0]} C:/Videos/smoke_scene.mp4")
        return

    input_video = sys.argv[1]

    # 运行演示
    success = simple_demo(input_video)

    if success:
        print()
        print("🎉 演示完成！您可以查看输出视频以观察去烟和人体检测效果。")
    else:
        print()
        print("💥 演示失败，请检查错误信息并重试。")

if __name__ == "__main__":
    main()