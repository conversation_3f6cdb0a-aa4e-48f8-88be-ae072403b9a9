#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频展示功能 - 浓烟环境人体目标判别系统

文件描述：
    实现视频的去烟处理和人体检测同步执行，输出带标注框的视频文件。
    使用多线程管道架构，提高处理效率。

主要功能：
    1. 视频帧级别的去烟处理
    2. 人体检测和标注框绘制
    3. 去烟和检测的并行处理
    4. 输出带标注的视频文件

作者：浓烟环境人体目标判别项目组
创建日期：2025年6月17日
版本：v1.0

依赖库：
    - OpenCV: 视频处理
    - ultralytics: YOLO模型推理
    - threading: 多线程处理
    - queue: 线程间通信
"""

import cv2
import numpy as np
import threading
import queue
import time
import os
from typing import Tuple, Optional, Dict, Any
from ultralytics import YOLO
from dehazing import DehazeProcessor
import argparse
from datetime import datetime

class VideoProcessor:
    """
    视频处理器 - 实现去烟和检测的同步处理

    架构设计：
        1. 视频读取线程：读取原始视频帧
        2. 去烟处理线程：对视频帧进行去烟处理
        3. 检测处理线程：对去烟后的帧进行人体检测
        4. 视频写入线程：将检测结果写入输出视频

    使用队列进行线程间通信，实现流水线处理。
    """

    def __init__(self,
                 model_path: str = "model/human_detection_model/weights/best.pt",
                 dehaze_method: str = "dcp",
                 device: str = "cpu",
                 queue_size: int = 30):
        """
        初始化视频处理器

        Args:
            model_path: YOLO模型路径
            dehaze_method: 去烟方法 ("dcp" 或 "aod")
            device: 计算设备 ("cpu" 或 "cuda")
            queue_size: 队列大小，控制内存使用
        """
        self.model_path = model_path
        self.dehaze_method = dehaze_method
        self.device = device
        self.queue_size = queue_size

        # 初始化模型和处理器
        self.yolo_model = None
        self.dehaze_processor = None

        # 线程间通信队列
        self.frame_queue = queue.Queue(maxsize=queue_size)      # 原始帧队列
        self.dehazed_queue = queue.Queue(maxsize=queue_size)    # 去烟后帧队列
        self.detected_queue = queue.Queue(maxsize=queue_size)   # 检测结果队列

        # 控制标志
        self.stop_flag = threading.Event()
        self.processing_stats = {
            "total_frames": 0,
            "processed_frames": 0,
            "start_time": None,
            "dehaze_times": [],
            "detection_times": []
        }

    def initialize_models(self):
        """初始化模型和处理器"""
        print("正在初始化模型...")

        # 初始化YOLO模型
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")

        self.yolo_model = YOLO(self.model_path)
        print(f"✓ YOLO模型已加载: {self.model_path}")

        # 初始化去烟处理器
        self.dehaze_processor = DehazeProcessor(
            method=self.dehaze_method,
            device=self.device
        )
        print(f"✓ 去烟处理器已初始化: {self.dehaze_method}")

    def read_video_frames(self, video_path: str):
        """
        视频读取线程
        读取视频帧并放入队列
        """
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"错误：无法打开视频文件 {video_path}")
            return

        # 获取视频信息
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        self.video_info = {
            "fps": fps,
            "total_frames": total_frames,
            "width": width,
            "height": height
        }
        self.processing_stats["total_frames"] = total_frames

        print(f"视频信息: {width}x{height}, {fps}FPS, {total_frames}帧")

        frame_idx = 0
        while not self.stop_flag.is_set():
            ret, frame = cap.read()
            if not ret:
                break

            try:
                # 将帧放入队列，包含帧索引
                self.frame_queue.put((frame_idx, frame), timeout=1.0)
                frame_idx += 1
            except queue.Full:
                print("警告：原始帧队列已满，跳过帧")
                continue

        cap.release()
        print("视频读取完成")

    def dehaze_frames(self):
        """
        去烟处理线程
        从原始帧队列取帧，处理后放入去烟队列
        """
        while not self.stop_flag.is_set():
            try:
                # 从队列获取帧
                frame_idx, frame = self.frame_queue.get(timeout=1.0)

                # 去烟处理
                start_time = time.time()
                dehazed_frame, dehaze_info = self.dehaze_processor.process(frame)
                dehaze_time = time.time() - start_time

                self.processing_stats["dehaze_times"].append(dehaze_time)

                # 将处理结果放入队列
                self.dehazed_queue.put((frame_idx, dehazed_frame, dehaze_info), timeout=1.0)

                # 标记任务完成
                self.frame_queue.task_done()

            except queue.Empty:
                continue
            except queue.Full:
                print("警告：去烟帧队列已满")
                continue

        print("去烟处理完成")

    def detect_humans(self):
        """
        人体检测线程
        从去烟队列取帧，检测后放入检测结果队列
        """
        while not self.stop_flag.is_set():
            try:
                # 从队列获取去烟后的帧
                frame_idx, dehazed_frame, dehaze_info = self.dehazed_queue.get(timeout=1.0)

                # 人体检测
                start_time = time.time()
                results = self.yolo_model(dehazed_frame, verbose=False)
                detection_time = time.time() - start_time

                self.processing_stats["detection_times"].append(detection_time)

                # 绘制检测框
                annotated_frame = self.draw_detections(dehazed_frame, results[0])

                # 将结果放入队列
                detection_info = {
                    "detection_time": detection_time,
                    "num_detections": len(results[0].boxes) if results[0].boxes is not None else 0
                }

                self.detected_queue.put((frame_idx, annotated_frame, dehaze_info, detection_info), timeout=1.0)

                # 标记任务完成
                self.dehazed_queue.task_done()

            except queue.Empty:
                continue
            except queue.Full:
                print("警告：检测结果队列已满")
                continue

        print("人体检测完成")

    def draw_detections(self, frame: np.ndarray, result) -> np.ndarray:
        """
        在帧上绘制检测框和标签

        Args:
            frame: 输入帧
            result: YOLO检测结果

        Returns:
            带标注的帧
        """
        annotated_frame = frame.copy()

        if result.boxes is not None:
            boxes = result.boxes.xyxy.cpu().numpy()
            confidences = result.boxes.conf.cpu().numpy()

            for box, conf in zip(boxes, confidences):
                x1, y1, x2, y2 = map(int, box)

                # 绘制边界框
                cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)

                # 绘制标签和置信度
                label = f"Person: {conf:.2f}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]

                # 绘制标签背景
                cv2.rectangle(annotated_frame,
                            (x1, y1 - label_size[1] - 10),
                            (x1 + label_size[0], y1),
                            (0, 255, 0), -1)

                # 绘制标签文字
                cv2.putText(annotated_frame, label,
                          (x1, y1 - 5),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)

        return annotated_frame

    def write_video(self, output_path: str):
        """
        视频写入线程
        从检测结果队列取帧，写入输出视频
        """
        # 初始化视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(
            output_path,
            fourcc,
            self.video_info["fps"],
            (self.video_info["width"], self.video_info["height"])
        )

        processed_frames = 0
        expected_frame_idx = 0
        frame_buffer = {}  # 用于处理乱序帧

        while not self.stop_flag.is_set() or not self.detected_queue.empty():
            try:
                # 从队列获取检测结果
                frame_idx, annotated_frame, dehaze_info, detection_info = self.detected_queue.get(timeout=1.0)

                # 处理帧顺序
                frame_buffer[frame_idx] = annotated_frame

                # 按顺序写入帧
                while expected_frame_idx in frame_buffer:
                    out.write(frame_buffer[expected_frame_idx])
                    del frame_buffer[expected_frame_idx]
                    expected_frame_idx += 1
                    processed_frames += 1

                    # 更新进度
                    self.processing_stats["processed_frames"] = processed_frames
                    if processed_frames % 30 == 0:  # 每30帧显示一次进度
                        progress = (processed_frames / self.processing_stats["total_frames"]) * 100
                        print(f"处理进度: {processed_frames}/{self.processing_stats['total_frames']} ({progress:.1f}%)")

                # 标记任务完成
                self.detected_queue.task_done()

            except queue.Empty:
                continue

        out.release()
        print(f"视频写入完成: {output_path}")

    def process_video(self, input_path: str, output_path: str) -> Dict[str, Any]:
        """
        处理视频的主函数

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径

        Returns:
            处理统计信息
        """
        print(f"开始处理视频: {input_path}")
        print(f"输出路径: {output_path}")

        # 初始化模型
        self.initialize_models()

        # 记录开始时间
        self.processing_stats["start_time"] = time.time()

        # 创建并启动线程
        threads = []

        # 视频读取线程
        read_thread = threading.Thread(target=self.read_video_frames, args=(input_path,))
        threads.append(read_thread)

        # 去烟处理线程
        dehaze_thread = threading.Thread(target=self.dehaze_frames)
        threads.append(dehaze_thread)

        # 人体检测线程
        detect_thread = threading.Thread(target=self.detect_humans)
        threads.append(detect_thread)

        # 视频写入线程
        write_thread = threading.Thread(target=self.write_video, args=(output_path,))
        threads.append(write_thread)

        # 启动所有线程
        for thread in threads:
            thread.start()

        try:
            # 等待读取线程完成
            read_thread.join()

            # 等待队列处理完成
            self.frame_queue.join()
            self.dehazed_queue.join()
            self.detected_queue.join()

            # 设置停止标志
            self.stop_flag.set()

            # 等待其他线程完成
            for thread in threads[1:]:
                thread.join()

        except KeyboardInterrupt:
            print("用户中断处理")
            self.stop_flag.set()

        # 计算统计信息
        total_time = time.time() - self.processing_stats["start_time"]

        stats = {
            "total_time": total_time,
            "total_frames": self.processing_stats["total_frames"],
            "processed_frames": self.processing_stats["processed_frames"],
            "fps": self.processing_stats["processed_frames"] / total_time if total_time > 0 else 0,
            "avg_dehaze_time": np.mean(self.processing_stats["dehaze_times"]) if self.processing_stats["dehaze_times"] else 0,
            "avg_detection_time": np.mean(self.processing_stats["detection_times"]) if self.processing_stats["detection_times"] else 0,
            "dehaze_method": self.dehaze_method,
            "model_path": self.model_path
        }

        print(f"\n=== 处理完成 ===")
        print(f"总时间: {total_time:.2f}秒")
        print(f"处理帧数: {stats['processed_frames']}/{stats['total_frames']}")
        print(f"平均FPS: {stats['fps']:.2f}")
        print(f"平均去烟时间: {stats['avg_dehaze_time']*1000:.2f}ms")
        print(f"平均检测时间: {stats['avg_detection_time']*1000:.2f}ms")

        return stats

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="浓烟环境人体检测视频展示")
    parser.add_argument("--input", "-i", required=True, help="输入视频路径")
    parser.add_argument("--output", "-o", required=True, help="输出视频路径")
    parser.add_argument("--model", "-m", default="model/human_detection_model/weights/best.pt", help="模型路径")
    parser.add_argument("--dehaze", "-d", choices=["dcp", "aod"], default="dcp", help="去烟方法")
    parser.add_argument("--device", choices=["cpu", "cuda"], default="cpu", help="计算设备")
    parser.add_argument("--queue-size", type=int, default=30, help="队列大小")

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.input):
        print(f"错误：输入视频文件不存在 {args.input}")
        return

    # 创建输出目录
    os.makedirs(os.path.dirname(args.output), exist_ok=True)

    # 创建处理器
    processor = VideoProcessor(
        model_path=args.model,
        dehaze_method=args.dehaze,
        device=args.device,
        queue_size=args.queue_size
    )

    # 处理视频
    try:
        stats = processor.process_video(args.input, args.output)

        # 保存统计信息
        stats_path = args.output.replace('.mp4', '_stats.json')
        import json
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        print(f"统计信息已保存到: {stats_path}")

    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()