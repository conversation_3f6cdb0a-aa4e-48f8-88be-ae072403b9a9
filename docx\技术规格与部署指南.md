# 浓烟环境人体目标判别系统 - 技术规格与部署指南

## 1. 模型技术规格

### 1.1 模型架构
- **模型类型**: YOLOv8n (You Only Look Once v8 Nano)
- **网络结构**: CSPDarknet53 + PANet + YOLOv8 Head
- **输入尺寸**: 640×640×3 (RGB图像)
- **输出格式**: 边界框坐标 + 置信度分数
- **检测类别**: 1类 (person - 人体)

### 1.2 模型参数
- **总参数量**: 3,005,843个
- **网络层数**: 72层 (融合后)
- **计算复杂度**: 8.1 GFLOPs
- **模型文件大小**: 5.94 MB
- **量化支持**: 支持FP16/INT8量化

### 1.3 性能指标
- **mAP@0.5**: 99.50%
- **mAP@0.5:0.95**: 95.25%
- **精度 (Precision)**: 99.96%
- **召回率 (Recall)**: 100.00%
- **F1分数**: 99.98%
- **推理速度**: 79.55 FPS (RTX 4060)
- **平均推理时间**: 12.57 ms

## 2. 系统要求

### 2.1 硬件要求

#### 最低配置
- **CPU**: Intel i5-8400 / AMD Ryzen 5 2600 或更高
- **内存**: 8GB RAM
- **存储**: 1GB可用空间
- **GPU**: 可选，CPU推理约5-10 FPS

#### 推荐配置
- **CPU**: Intel i7-10700 / AMD Ryzen 7 3700X 或更高
- **内存**: 16GB RAM
- **存储**: 2GB可用空间 (包含数据集)
- **GPU**: NVIDIA GTX 1660 / RTX 3060 或更高 (4GB+ 显存)

#### 高性能配置
- **CPU**: Intel i9-12900K / AMD Ryzen 9 5900X 或更高
- **内存**: 32GB RAM
- **存储**: SSD 5GB可用空间
- **GPU**: NVIDIA RTX 4060 / RTX 4070 或更高 (8GB+ 显存)

### 2.2 软件要求
- **操作系统**: Windows 10/11, Ubuntu 18.04+, macOS 10.15+
- **Python版本**: 3.8 - 3.11
- **CUDA版本**: 11.8+ (GPU推理)
- **依赖库**: 见requirements.txt

## 3. 安装部署指南

### 3.1 环境准备

#### 创建虚拟环境
```bash
# 使用conda
conda create -n human_detection python=3.10
conda activate human_detection

# 或使用venv
python -m venv human_detection
source human_detection/bin/activate  # Linux/Mac
# human_detection\Scripts\activate   # Windows
```

#### 安装依赖
```bash
pip install ultralytics
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
pip install opencv-python
pip install pillow
pip install numpy
pip install matplotlib
```

### 3.2 模型部署

#### 基础推理代码
```python
from ultralytics import YOLO
import cv2

# 加载模型
model = YOLO('model/human_detection_model/weights/best.pt')

# 单张图像推理
results = model('path/to/image.jpg')

# 批量推理
results = model(['image1.jpg', 'image2.jpg'])

# 视频推理
results = model('path/to/video.mp4')

# 实时摄像头推理
results = model(source=0, show=True)
```

#### 高级配置
```python
# 自定义推理参数
results = model(
    source='image.jpg',
    conf=0.25,          # 置信度阈值
    iou=0.45,           # NMS IoU阈值
    max_det=300,        # 最大检测数量
    device='0',         # GPU设备
    half=True,          # FP16推理
    save=True,          # 保存结果
    save_txt=True,      # 保存标签文件
    save_conf=True      # 保存置信度
)
```

### 3.3 API服务部署

#### Flask API示例
```python
from flask import Flask, request, jsonify
from ultralytics import YOLO
import cv2
import base64
import numpy as np

app = Flask(__name__)
model = YOLO('model/human_detection_model/weights/best.pt')

@app.route('/detect', methods=['POST'])
def detect_humans():
    # 接收base64编码的图像
    image_data = request.json['image']
    image_bytes = base64.b64decode(image_data)
    
    # 转换为OpenCV格式
    nparr = np.frombuffer(image_bytes, np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    
    # 推理
    results = model(image)
    
    # 提取检测结果
    detections = []
    for r in results:
        boxes = r.boxes
        if boxes is not None:
            for box in boxes:
                x1, y1, x2, y2 = box.xyxy[0].tolist()
                conf = box.conf[0].item()
                detections.append({
                    'bbox': [x1, y1, x2, y2],
                    'confidence': conf,
                    'class': 'person'
                })
    
    return jsonify({'detections': detections})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
```

## 4. 性能优化

### 4.1 推理优化

#### TensorRT优化 (NVIDIA GPU)
```python
# 导出TensorRT模型
model.export(format='engine', device=0, half=True)

# 使用TensorRT模型
model_trt = YOLO('best.engine')
results = model_trt('image.jpg')
```

#### ONNX优化
```python
# 导出ONNX模型
model.export(format='onnx', opset=11)

# 使用ONNX Runtime
import onnxruntime as ort
session = ort.InferenceSession('best.onnx')
```

#### 量化优化
```python
# INT8量化
model.export(format='engine', int8=True, data='dataset.yaml')
```

### 4.2 批处理优化
```python
# 批量推理提高吞吐量
batch_size = 8
images = ['img1.jpg', 'img2.jpg', ...]
for i in range(0, len(images), batch_size):
    batch = images[i:i+batch_size]
    results = model(batch)
```

## 5. 监控和维护

### 5.1 性能监控
```python
import time
import psutil
import GPUtil

def monitor_performance():
    # CPU使用率
    cpu_percent = psutil.cpu_percent()
    
    # 内存使用率
    memory = psutil.virtual_memory()
    
    # GPU使用率
    gpus = GPUtil.getGPUs()
    gpu_load = gpus[0].load * 100 if gpus else 0
    
    return {
        'cpu_percent': cpu_percent,
        'memory_percent': memory.percent,
        'gpu_percent': gpu_load
    }
```

### 5.2 日志记录
```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('detection.log'),
        logging.StreamHandler()
    ]
)

# 记录检测结果
def log_detection(image_path, detections, inference_time):
    logging.info(f"Image: {image_path}, "
                f"Detections: {len(detections)}, "
                f"Time: {inference_time:.3f}s")
```

## 6. 故障排除

### 6.1 常见问题

#### CUDA内存不足
```python
# 解决方案：减少批次大小或使用CPU
model = YOLO('best.pt')
results = model('image.jpg', device='cpu')
```

#### 推理速度慢
```python
# 解决方案：启用半精度推理
results = model('image.jpg', half=True)
```

#### 检测精度低
```python
# 解决方案：调整置信度阈值
results = model('image.jpg', conf=0.1)  # 降低阈值
```

### 6.2 性能基准测试
```python
def benchmark_model(model_path, test_images, num_runs=100):
    model = YOLO(model_path)
    times = []
    
    for _ in range(num_runs):
        start_time = time.time()
        results = model(test_images[0])
        end_time = time.time()
        times.append(end_time - start_time)
    
    avg_time = np.mean(times)
    fps = 1.0 / avg_time
    
    print(f"平均推理时间: {avg_time*1000:.2f} ms")
    print(f"推理FPS: {fps:.2f}")
    
    return avg_time, fps
```

## 7. 扩展和定制

### 7.1 多类别扩展
如需扩展为多类别检测，需要：
1. 重新标注数据集
2. 修改dataset.yaml中的类别数量
3. 重新训练模型

### 7.2 模型微调
```python
# 在新数据上微调
model = YOLO('model/human_detection_model/weights/best.pt')
model.train(
    data='new_dataset.yaml',
    epochs=50,
    lr0=0.0001,  # 较小的学习率
    freeze=10    # 冻结前10层
)
```

### 7.3 自定义后处理
```python
def custom_postprocess(results, min_area=100):
    """自定义后处理：过滤小目标"""
    filtered_results = []
    for r in results:
        boxes = r.boxes
        if boxes is not None:
            for box in boxes:
                x1, y1, x2, y2 = box.xyxy[0].tolist()
                area = (x2 - x1) * (y2 - y1)
                if area >= min_area:
                    filtered_results.append(box)
    return filtered_results
```

## 8. 联系和支持

- **项目仓库**: [GitHub链接]
- **技术文档**: [文档链接]
- **问题反馈**: [Issues链接]
- **技术支持**: [邮箱地址]

---

**文档版本**: v1.0  
**最后更新**: 2025年6月14日  
**适用模型**: YOLOv8n-human-detection-v1.0
