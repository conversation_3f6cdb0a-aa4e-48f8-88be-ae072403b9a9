# 暗通道先验去烟算法流程表

## 算法概述
基于项目中 `dehazing.py` 文件实现的暗通道先验(Dark Channel Prior)去烟算法详细流程。

## 主要步骤流程表

| 步骤 | 操作名称 | 具体实现 | 关键参数 | 输入 | 输出 |
|------|----------|----------|----------|------|------|
| 1 | 初始化参数 | 设置算法核心参数 | patch_size=15<br/>omega=0.95<br/>t0=0.1 | 无 | 参数配置 |
| 2 | 计算暗通道 | get_dark_channel() | patch_size=15 | BGR图像(H,W,3) | 暗通道图(H,W) |
| 2.1 | RGB最小值 | np.min(image, axis=2) | 无 | BGR图像 | 最小值通道 |
| 2.2 | 形态学腐蚀 | cv2.erode(min_channel, kernel) | 矩形核15×15 | 最小值通道 | 暗通道 |
| 3 | 估计大气光 | estimate_atmospheric_light() | 选择0.1%最亮像素 | 原图+暗通道 | 大气光值(3,) |
| 3.1 | 选择像素 | argpartition(-num_brightest) | num_brightest=max(1,pixels//1000) | 暗通道 | 像素索引 |
| 3.2 | 提取像素值 | image[y,x] | 无 | 原图+索引 | 最亮像素值 |
| 3.3 | 计算均值 | np.mean(brightest_pixels) | 无 | 最亮像素 | 大气光A |
| 4 | 估计透射率 | estimate_transmission() | omega=0.95 | 原图+大气光 | 透射率图(H,W) |
| 4.1 | 图像归一化 | image/(atmospheric_light+1e-8) | 防除零: 1e-8 | 原图+大气光 | 归一化图像 |
| 4.2 | 计算暗通道 | get_dark_channel(normalized) | patch_size=15 | 归一化图像 | 归一化暗通道 |
| 4.3 | 透射率公式 | 1 - omega × dark_channel | omega=0.95 | 归一化暗通道 | 透射率 |
| 5 | 细化透射率 | refine_transmission() | radius=60<br/>eps=0.0001 | 原图+透射率 | 细化透射率 |
| 5.1 | 导向图转换 | cvtColor(BGR2GRAY)/255.0 | 无 | 原图 | 灰度导向图 |
| 5.2 | 导向滤波 | guidedFilter(guide,transmission) | radius=60<br/>eps=0.0001 | 导向图+透射率 | 细化透射率 |
| 6 | 场景恢复 | recover_scene() | t0=0.1 | 原图+透射率+大气光 | 去烟图像 |
| 6.1 | 透射率下限 | np.maximum(transmission, t0) | t0=0.1 | 透射率 | 限制透射率 |
| 6.2 | 扩展维度 | np.stack([transmission]*3) | 无 | 透射率(H,W) | 透射率(H,W,3) |
| 6.3 | 恢复公式 | (I-A)/t + A | 对每个通道 | 原图+透射率+大气光 | 恢复图像 |
| 6.4 | 像素限制 | np.clip(recovered, 0, 255) | 范围[0,255] | 恢复图像 | 最终结果 |
| 7 | 生成信息 | 记录处理信息 | 无 | 处理过程数据 | 信息字典 |

## 数学公式表

| 公式名称 | 数学表达式 | 说明 | 代码实现 |
|----------|------------|------|----------|
| 大气散射模型 | I(x) = J(x)t(x) + A(1-t(x)) | I:观测图像, J:场景辐射, t:透射率, A:大气光 | 理论基础 |
| 暗通道定义 | J^dark(x) = min_{c∈{r,g,b}}(min_{y∈Ω(x)}(J^c(y))) | 局部邻域内RGB最小值的最小值 | np.min + cv2.erode |
| 透射率估计 | t̃(x) = 1 - ω × (I^dark(x)/A) | ω为去雾强度参数 | 1 - omega × dark_channel |
| 场景恢复 | J(x) = (I(x) - A)/max(t(x), t₀) + A | t₀为透射率下限 | (image - A) / transmission + A |
| 导向滤波 | q_i = a_k × I_i + b_k | 边缘保持平滑 | cv2.ximgproc.guidedFilter |

## 参数配置表

| 参数名称 | 默认值 | 取值范围 | 作用说明 | 调整建议 |
|----------|--------|----------|----------|----------|
| patch_size | 15 | [3, 31] | 暗通道计算邻域大小 | 浓烟环境可适当增大到21 |
| omega | 0.95 | [0.8, 1.0] | 去雾强度控制 | 浓烟环境建议0.9-0.95 |
| t0 | 0.1 | [0.05, 0.2] | 透射率下限阈值 | 防止过度去雾，保持0.1 |
| radius | 60 | [20, 100] | 导向滤波半径 | 影响平滑程度 |
| eps | 0.0001 | [1e-5, 1e-3] | 导向滤波正则化 | 控制边缘保持能力 |

## 性能指标表

| 指标类型 | 计算方法 | 评估标准 | 项目表现 |
|----------|----------|----------|----------|
| 处理时间 | time.time()计时 | 越短越好 | 实时处理能力 |
| 大气光估计 | 最亮0.1%像素均值 | 接近真实大气光 | 自动估计 |
| 透射率统计 | mean/std计算 | 合理分布 | 记录统计信息 |
| 图像质量 | PSNR/SSIM等 | 越高越好 | 支持质量评估 |

## 算法优势表

| 优势特点 | 具体表现 | 适用场景 |
|----------|----------|----------|
| 无需训练 | 基于物理模型 | 即插即用 |
| 处理速度快 | 传统图像处理 | 实时应用 |
| 浓烟效果好 | 针对性优化 | 烟雾环境 |
| 参数可调 | 灵活配置 | 不同场景 |
| 鲁棒性强 | 物理约束 | 稳定可靠 |

## 实现细节表

| 实现方面 | 技术选择 | 原因说明 |
|----------|----------|----------|
| 图像格式 | BGR (OpenCV) | 兼容性好 |
| 数据类型 | float64精度 | 避免精度损失 |
| 边界处理 | np.clip限制 | 防止溢出 |
| 异常处理 | try-except | 导向滤波可选 |
| 内存优化 | 就地操作 | 提高效率 |
