#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频演示功能测试脚本

用于测试video_demo.py的基本功能，验证模块导入和基本配置
"""

import os
import sys

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")

    try:
        import cv2
        print("✅ OpenCV 导入成功")
    except ImportError as e:
        print(f"❌ OpenCV 导入失败: {e}")
        return False

    try:
        from ultralytics import YOLO
        print("✅ Ultralytics 导入成功")
    except ImportError as e:
        print(f"❌ Ultralytics 导入失败: {e}")
        return False

    try:
        from dehazing import DehazeProcessor
        print("✅ 去烟处理器导入成功")
    except ImportError as e:
        print(f"❌ 去烟处理器导入失败: {e}")
        return False

    try:
        from video_demo import VideoProcessor
        print("✅ 视频处理器导入成功")
    except ImportError as e:
        print(f"❌ 视频处理器导入失败: {e}")
        return False

    return True

def test_model_files():
    """测试模型文件是否存在"""
    print("\n🔍 检查模型文件...")

    model_path = "model/human_detection_model/weights/best.pt"
    if os.path.exists(model_path):
        print(f"✅ 模型文件存在: {model_path}")
        return True
    else:
        print(f"❌ 模型文件不存在: {model_path}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n🔍 测试基本功能...")

    try:
        from video_demo import VideoProcessor

        # 创建处理器实例
        processor = VideoProcessor(
            model_path="model/human_detection_model/weights/best.pt",
            dehaze_method="dcp",
            device="cpu",
            queue_size=5
        )
        print("✅ 视频处理器创建成功")

        # 测试去烟处理器初始化
        from dehazing import DehazeProcessor
        dehaze_processor = DehazeProcessor(method="dcp", device="cpu")
        print("✅ 去烟处理器初始化成功")

        return True

    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 视频演示功能测试")
    print("=" * 60)

    # 测试模块导入
    if not test_imports():
        print("\n💥 模块导入测试失败，请检查依赖安装")
        return False

    # 测试模型文件
    if not test_model_files():
        print("\n💥 模型文件检查失败，请确保模型已训练")
        return False

    # 测试基本功能
    if not test_basic_functionality():
        print("\n💥 基本功能测试失败")
        return False

    print("\n" + "=" * 60)
    print("🎉 所有测试通过！视频演示功能准备就绪")
    print("=" * 60)
    print("\n📖 使用方法:")
    print("   python demo_simple.py your_video.mp4")
    print("   python video_demo.py -i input.mp4 -o output.mp4")

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)