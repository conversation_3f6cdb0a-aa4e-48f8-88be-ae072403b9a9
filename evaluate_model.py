#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型评估脚本 - 浓烟环境人体目标判别系统

文件描述：
    对训练好的模型进行详细评估，包括测试集性能、推理速度等指标。

作者：浓烟环境人体目标判别项目组
创建日期：2025年6月15日
"""

import os
import time
import numpy as np
from ultralytics import YOLO
from datetime import datetime
import json

def evaluate_model_performance():
    """评估模型性能"""
    
    # 加载训练好的最佳模型
    model_path = 'model/human_detection_model/weights/best.pt'
    if not os.path.exists(model_path):
        print(f"错误：模型文件不存在 {model_path}")
        return None
    
    model = YOLO(model_path)
    
    print("开始模型评估...")
    start_time = datetime.now()
    
    # 在验证集上评估
    print("在验证集上评估...")
    val_results = model.val(data='dataset.yaml', split='val')
    
    # 在测试集上评估
    print("在测试集上评估...")
    test_results = model.val(data='dataset.yaml', split='test')
    
    # 推理速度测试
    print("进行推理速度测试...")
    speed_results = test_inference_speed(model)
    
    end_time = datetime.now()
    evaluation_time = end_time - start_time
    
    # 整理评估结果
    evaluation_report = {
        'evaluation_time': str(evaluation_time),
        'model_path': model_path,
        'validation_results': {
            'mAP50': float(val_results.box.map50),
            'mAP50_95': float(val_results.box.map),
            'precision': float(val_results.box.mp),
            'recall': float(val_results.box.mr),
            'f1_score': float(2 * val_results.box.mp * val_results.box.mr / (val_results.box.mp + val_results.box.mr))
        },
        'test_results': {
            'mAP50': float(test_results.box.map50),
            'mAP50_95': float(test_results.box.map),
            'precision': float(test_results.box.mp),
            'recall': float(test_results.box.mr),
            'f1_score': float(2 * test_results.box.mp * test_results.box.mr / (test_results.box.mp + test_results.box.mr))
        },
        'inference_speed': speed_results
    }
    
    # 保存评估报告
    os.makedirs('evaluation_results', exist_ok=True)
    with open('evaluation_results/evaluation_report.json', 'w', encoding='utf-8') as f:
        json.dump(evaluation_report, f, ensure_ascii=False, indent=2)
    
    print(f"评估完成！总耗时: {evaluation_time}")
    print(f"评估报告已保存到: evaluation_results/evaluation_report.json")
    
    return evaluation_report

def test_inference_speed(model, num_tests=100):
    """测试推理速度"""
    
    # 使用测试集中的第一张图像进行速度测试
    test_image_path = 'wuxi_video_2_dehazed/images/test'
    test_images = [f for f in os.listdir(test_image_path) if f.endswith('.jpg')]
    
    if not test_images:
        print("警告：未找到测试图像")
        return None
    
    test_image = os.path.join(test_image_path, test_images[0])
    
    # 预热
    for _ in range(10):
        model(test_image, verbose=False)
    
    # 正式测试
    times = []
    for i in range(num_tests):
        start_time = time.time()
        results = model(test_image, verbose=False)
        end_time = time.time()
        times.append(end_time - start_time)
    
    speed_stats = {
        'average_time_ms': float(np.mean(times) * 1000),
        'min_time_ms': float(np.min(times) * 1000),
        'max_time_ms': float(np.max(times) * 1000),
        'std_time_ms': float(np.std(times) * 1000),
        'fps': float(1.0 / np.mean(times)),
        'num_tests': num_tests
    }
    
    return speed_stats

if __name__ == "__main__":
    evaluation_report = evaluate_model_performance()
    
    if evaluation_report:
        print("\n=== 评估结果摘要 ===")
        print(f"验证集 mAP@0.5: {evaluation_report['validation_results']['mAP50']:.4f}")
        print(f"验证集 mAP@0.5:0.95: {evaluation_report['validation_results']['mAP50_95']:.4f}")
        print(f"测试集 mAP@0.5: {evaluation_report['test_results']['mAP50']:.4f}")
        print(f"测试集 mAP@0.5:0.95: {evaluation_report['test_results']['mAP50_95']:.4f}")
        print(f"平均推理时间: {evaluation_report['inference_speed']['average_time_ms']:.2f} ms")
        print(f"推理FPS: {evaluation_report['inference_speed']['fps']:.2f}")
