#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练脚本 - 浓烟环境人体目标判别系统

文件描述：
    使用YOLOv8n模型在去烟雾处理后的数据集上训练人体检测模型。

作者：浓烟环境人体目标判别项目组
创建日期：2025年6月15日
"""

import os
from ultralytics import YOLO
from datetime import datetime

def train_smoke_detection_model():
    """训练烟雾环境下的人体检测模型"""
    
    # 创建结果目录
    os.makedirs('model', exist_ok=True)
    
    # 加载预训练的YOLOv8n模型
    model = YOLO('yolov8n.pt')
    
    # 训练模型
    results = model.train(
        data='dataset.yaml',      # 数据集配置
        epochs=100,                        # 训练轮次
        imgsz=640,                       # 图像大小
        batch=16,                        # 批次大小
        workers=4,                       # 数据加载线程数
        device='0',                      # GPU设备
        project='model',            # 保存结果的项目名
        name='human_detection_model',    # 实验名称
        pretrained=True,                 # 使用预训练权重
        optimizer='SGD',                # 优化器
        lr0=0.001,                       # 初始学习率
        single_cls=True,                 # 单类别模式
        patience=50,                     # 早停耐心值
        save=True,                       # 保存模型
        plots=True                       # 生成训练图表
    )
    
    # 返回训练结果
    return results, model

def evaluate_model(model):
    """评估训练好的模型"""
    # 在验证集上评估模型
    val_results = model.val()
    
    # 打印评估结果
    print(f"mAP@0.5: {val_results.box.map50:.4f}")
    print(f"mAP@0.5:0.95: {val_results.box.map:.4f}")
    
    return val_results

if __name__ == "__main__":
    print("开始训练浓烟环境人体检测模型...")
    start_time = datetime.now()
    
    # 训练模型
    results, model = train_smoke_detection_model()
    
    # 评估模型
    val_results = evaluate_model(model)
    
    # 打印训练时间
    end_time = datetime.now()
    training_time = end_time - start_time
    print(f"训练完成！总耗时: {training_time}")
    
    print("模型已保存到: model/human_detection_model")